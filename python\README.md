# Question Service API (Python)

A Python-based web service for AI-powered programming question generation, converted from the original Go implementation. This service provides RESTful APIs for generating, managing, and querying programming questions using AI models (DeepSeek and Tongyi).

## Features

- **AI Question Generation**: Generate programming questions using DeepSeek or Tongyi AI models
- **Question Management**: CRUD operations for questions with different types (single-select, multi-select, coding)
- **Database Operations**: SQLite storage with async operations and transaction support
- **RESTful API**: FastAPI-based web service with automatic API documentation
- **CORS Support**: Cross-origin resource sharing for frontend integration
- **Static File Serving**: Serves frontend assets automatically

## Project Structure

```
python/
├── app/
│   ├── __init__.py
│   ├── config/
│   │   ├── __init__.py
│   │   └── config.py          # Configuration management
│   ├── services/
│   │   ├── __init__.py
│   │   ├── client.py          # AI service interface
│   │   ├── deepseek.py        # DeepSeek AI client
│   │   └── tongyi.py          # Tongyi AI client
│   ├── storage/
│   │   ├── __init__.py
│   │   └── database.py        # Database operations
│   ├── controllers/
│   │   ├── __init__.py
│   │   ├── question.py        # Question generation endpoints
│   │   └── actions.py         # Question management endpoints
│   └── api/
│       ├── __init__.py
│       └── response.py        # API response utilities
├── main.py                    # Application entry point
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
└── README.md                 # This file
```

## Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)

### Setup Steps

1. **Clone or copy the project files**

   ```bash
   cd python
   ```

2. **Create a virtual environment (recommended)**

   ```bash
   python -m venv venv

   # On Windows
   venv\Scripts\activate

   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**

   ```bash
   cp .env.example .env
   ```

   Edit `.env` file and add your API keys:

   ```env
   # At least one API key is required
   DEEPSEEK_API_KEY=your_deepseek_api_key_here
   TONGYI_API_KEY=your_tongyi_api_key_here

   # Optional configurations
   API_TIMEOUT=30
   PORT=8080
   ```

5. **Run the application**
   ```bash
   python main.py
   ```

The server will start on `http://localhost:8080`

## API Documentation

Once the server is running, you can access:

- **Interactive API Documentation**: http://localhost:8080/docs
- **Alternative API Documentation**: http://localhost:8080/redoc

### Main Endpoints

#### Question Generation

**POST** `/api/questions/CreateByAI`

- Generate questions using AI
- Request body:
  ```json
  {
    "keyword": "golang并发",
    "model": "tongyi",
    "language": "go",
    "count": 3,
    "type": 1
  }
  ```

**POST** `/api/questions/batch-insert`

- Batch insert questions into database

#### Question Management

**POST** `/api/questions/CreateByHand`

- Create question manually

**PUT** `/api/questions/update`

- Update existing question

**DELETE** `/api/questions/batch-delete`

- Batch delete questions

#### Statistics and Queries

**GET** `/api/stats/summary`

- Get all questions with pagination

**GET** `/api/stats/bytype1`

- Get single-select questions

**GET** `/api/stats/bytype2`

- Get multi-select questions

**GET** `/api/stats/bytype3`

- Get coding questions

**GET** `/api/stats/byid/{id}`

- Get question by ID

**GET** `/api/health`

- Health check endpoint

### Request Parameters

#### Question Types

- `1`: Single-select questions
- `2`: Multi-select questions
- `3`: Coding questions

#### Supported Languages

- `go`, `java`, `python`, `javascript`, `c++`, `css`, `html`

#### AI Models

- `tongyi`: Tongyi Qianwen (default)
- `deepseek`: DeepSeek AI

## Usage Examples

### Generate Questions with AI

```bash
curl -X POST "http://localhost:8080/api/questions/CreateByAI" \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "python列表",
    "model": "tongyi",
    "language": "python",
    "count": 3,
    "type": 2
  }'
```

### Get Questions by Type

```bash
curl "http://localhost:8080/api/stats/bytype1?page=1&pageSize=10&search=golang"
```

### Create Question Manually

```bash
curl -X POST "http://localhost:8080/api/questions/CreateByHand" \
  -H "Content-Type: application/json" \
  -d '{
    "type": 1,
    "title": "Go语言中哪个关键字用于声明变量？",
    "language": "go",
    "answers": ["A: var", "B: let", "C: const", "D: define"],
    "rights": ["A"]
  }'
```

## Development

### Code Style

This project follows Python best practices:

- **PEP 8** compliance for code formatting
- **snake_case** for variables and functions
- **PascalCase** for classes
- **4 spaces** for indentation
- **Line length ≤ 88** characters
- **f-strings** for string formatting

### Running Tests

```bash
# Install test dependencies (if not already installed)
pip install pytest pytest-asyncio

# Run tests
pytest
```

### Code Formatting

```bash
# Format code with black
black .

# Check code style with flake8
flake8 .
```

## Differences from Go Version

This Python implementation maintains the same API interface and functionality as the original Go version, with the following key differences:

1. **Framework**: Uses FastAPI instead of Gin
2. **Database**: Uses aiosqlite for async SQLite operations instead of sqlx
3. **HTTP Client**: Uses httpx instead of go-openai client
4. **Configuration**: Uses python-dotenv instead of godotenv
5. **Logging**: Excludes logging/log storage functionality as per requirements
6. **Async/Await**: Fully async implementation using Python's asyncio

## Troubleshooting

### Common Issues

1. **Import Errors**

   - Ensure you're in the correct directory and virtual environment is activated
   - Check that all dependencies are installed: `pip install -r requirements.txt`

2. **API Key Errors**

   - Verify at least one API key is configured in `.env` file
   - Check API key validity and permissions

3. **Database Errors**

   - Ensure write permissions in the application directory
   - Database file will be created automatically on first run

4. **Port Already in Use**
   - Change the PORT in `.env` file or kill the process using the port

### Logs

The application logs important events to the console. Check the console output for detailed error messages and debugging information.

## License

This project maintains the same license as the original Go implementation.
