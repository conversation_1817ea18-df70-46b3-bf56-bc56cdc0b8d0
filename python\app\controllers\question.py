"""
Question controller for AI-generated question endpoints.
Handles question generation and batch operations.
"""

import time
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from app.config.config import QuestionRequest, validate_question_request
from app.services.client import AIService
from app.storage.database import Database
from app.api.response import success_response, error_response


class QuestionGenerationRequest(BaseModel):
    """Request model for AI question generation."""
    keyword: str = Field(..., description="关键字")
    model: str = Field("tongyi", description="AI模型")
    language: str = Field("go", description="编程语言")
    count: int = Field(3, ge=3, le=10, description="题目数量")
    type: int = Field(1, ge=1, le=3, description="题目类型")


class BatchInsertRequest(BaseModel):
    """Request model for batch question insertion."""
    questions: List[Dict[str, Any]] = Field(..., description="题目列表")


class QuestionController:
    """Controller for question-related operations."""
    
    def __init__(self, ai_service: AIService, database: Database):
        """
        Initialize question controller.
        
        Args:
            ai_service: AI service instance
            database: Database instance
        """
        self.ai_service = ai_service
        self.database = database
        self.router = APIRouter()
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup API routes."""
        self.router.post("/CreateByAI")(self.generate_question)
        self.router.post("/batch-insert")(self.add_questions)
    
    async def generate_question(self, request: QuestionGenerationRequest):
        """
        Generate questions using AI service.
        
        Args:
            request: Question generation request
            
        Returns:
            Generated questions response
        """
        start_time = time.time()
        
        try:
            # Convert to internal request format
            ai_request = QuestionRequest(
                keyword=request.keyword,
                model=request.model,
                language=request.language,
                count=request.count,
                type=request.type
            )
            
            # Validate request
            ai_request = validate_question_request(ai_request)
            
            # Generate questions using AI service
            response = await self.ai_service.generate_question(ai_request)
            
            # Note: Logging functionality is excluded as per requirements
            
            return success_response({
                "aiRes": {
                    "questions": [
                        {
                            "title": q.title,
                            "answers": q.answers,
                            "rights": q.rights
                        }
                        for q in response.questions
                    ]
                }
            })
            
        except ValueError as e:
            raise error_response(f"参数错误: {str(e)}", 400)
        except Exception as e:
            raise error_response(f"生成失败: {str(e)}", 500)
    
    async def add_questions(self, request: BatchInsertRequest):
        """
        Batch insert questions into database.
        
        Args:
            request: Batch insert request
            
        Returns:
            Success response
        """
        try:
            # Validate questions format
            for i, question in enumerate(request.questions):
                required_fields = ["type", "title", "language", "answers", "rights"]
                for field in required_fields:
                    if field not in question:
                        raise ValueError(f"题目 {i+1} 缺少必需字段: {field}")
                
                # Validate question type
                if question["type"] not in [1, 2, 3]:
                    raise ValueError(f"题目 {i+1} 类型无效")
                
                # Validate answers format
                if not isinstance(question["answers"], list):
                    raise ValueError(f"题目 {i+1} 选项格式错误")
                
                if not isinstance(question["rights"], list):
                    raise ValueError(f"题目 {i+1} 答案格式错误")
            
            # Batch insert questions
            await self.database.batch_insert_questions(request.questions)
            
            return success_response(None, "添加成功")
            
        except ValueError as e:
            raise error_response(f"参数错误: {str(e)}", 400)
        except Exception as e:
            raise error_response(f"存储失败: {str(e)}", 500)


def create_question_controller(ai_service: AIService, database: Database) -> APIRouter:
    """
    Factory function to create question controller router.
    
    Args:
        ai_service: AI service instance
        database: Database instance
        
    Returns:
        Configured APIRouter
    """
    controller = QuestionController(ai_service, database)
    return controller.router
