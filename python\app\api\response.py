"""
API response utilities for consistent response formatting.
"""

from typing import Any, Dict
from fastapi import HTTPException
from fastapi.responses import JSONResponse


class APIResponse:
    """Utility class for standardized API responses."""
    
    @staticmethod
    def success(data: Any = None, message: str = "success") -> Dict[str, Any]:
        """
        Create a success response.
        
        Args:
            data: Response data
            message: Success message
            
        Returns:
            Standardized success response dictionary
        """
        return {
            "code": 0,
            "msg": message,
            "data": data
        }
    
    @staticmethod
    def error(message: str, code: int = -1) -> Dict[str, Any]:
        """
        Create an error response.
        
        Args:
            message: Error message
            code: Error code
            
        Returns:
            Standardized error response dictionary
        """
        return {
            "code": code,
            "msg": message,
            "data": None
        }


def success_response(data: Any = None, message: str = "success") -> JSONResponse:
    """
    Create a successful JSON response.
    
    Args:
        data: Response data
        message: Success message
        
    Returns:
        JSONResponse with success format
    """
    return JSONResponse(
        status_code=200,
        content=APIResponse.success(data, message)
    )


def error_response(message: str, status_code: int = 400, error_code: int = -1) -> HTTPException:
    """
    Create an error HTTP exception.
    
    Args:
        message: Error message
        status_code: HTTP status code
        error_code: Application error code
        
    Returns:
        HTTPException with error format
    """
    return HTTPException(
        status_code=status_code,
        detail=APIResponse.error(message, error_code)
    )
