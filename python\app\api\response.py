"""
API响应工具模块，用于统一的响应格式化。
"""

from typing import Any, Dict
from fastapi import HTTPException
from fastapi.responses import JSONResponse


class APIResponse:
    """标准化API响应的工具类。"""

    @staticmethod
    def success(data: Any = None, message: str = "success") -> Dict[str, Any]:
        """
        创建成功响应。

        Args:
            data: 响应数据
            message: 成功消息

        Returns:
            标准化的成功响应字典
        """
        return {
            "code": 0,
            "msg": message,
            "data": data
        }

    @staticmethod
    def error(message: str, code: int = -1) -> Dict[str, Any]:
        """
        创建错误响应。

        Args:
            message: 错误消息
            code: 错误代码

        Returns:
            标准化的错误响应字典
        """
        return {
            "code": code,
            "msg": message,
            "data": None
        }


def success_response(data: Any = None, message: str = "success") -> JSONResponse:
    """
    创建成功的JSON响应。

    Args:
        data: 响应数据
        message: 成功消息

    Returns:
        成功格式的JSONResponse
    """
    return JSONResponse(
        status_code=200,
        content=APIResponse.success(data, message)
    )


def error_response(message: str, status_code: int = 400, error_code: int = -1) -> HTTPException:
    """
    创建错误的HTTP异常。

    Args:
        message: 错误消息
        status_code: HTTP状态码
        error_code: 应用错误代码

    Returns:
        错误格式的HTTPException
    """
    return HTTPException(
        status_code=status_code,
        detail=APIResponse.error(message, error_code)
    )
