"""
Database operations for the Question Service.
Handles SQLite database connections and CRUD operations.
"""

import j<PERSON>
import sqlite3
from typing import List, Dict, Any, Optional, Tuple
from contextlib import asynccontextmanager
import aiosqlite

from app.config.config import QuestionRequest1


CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS questions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    type INTEGER NOT NULL,
    language TEXT NOT NULL,
    answers TEXT NOT NULL,
    rights TEXT NOT NULL
);
"""


class Database:
    """Database wrapper for SQLite operations."""
    
    def __init__(self, db_path: str):
        """
        Initialize database connection.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path
    
    async def init_db(self) -> None:
        """Initialize database and create tables."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(CREATE_TABLE_SQL)
            await db.commit()
    
    async def close(self) -> None:
        """Close database connection (placeholder for compatibility)."""
        pass
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection context manager."""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            yield db
    
    async def select(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        Execute SELECT query and return results.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of dictionaries representing rows
        """
        async with self.get_connection() as db:
            cursor = await db.execute(query, params)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]
    
    async def get(self, query: str, params: tuple = ()) -> Optional[Dict[str, Any]]:
        """
        Execute SELECT query and return single result.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Dictionary representing single row or None
        """
        async with self.get_connection() as db:
            cursor = await db.execute(query, params)
            row = await cursor.fetchone()
            return dict(row) if row else None
    
    async def execute(self, query: str, params: tuple = ()) -> int:
        """
        Execute INSERT/UPDATE/DELETE query.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Number of affected rows
        """
        async with self.get_connection() as db:
            cursor = await db.execute(query, params)
            await db.commit()
            return cursor.rowcount
    
    async def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """
        Execute query with multiple parameter sets.
        
        Args:
            query: SQL query string
            params_list: List of parameter tuples
            
        Returns:
            Number of affected rows
        """
        async with self.get_connection() as db:
            cursor = await db.executemany(query, params_list)
            await db.commit()
            return cursor.rowcount
    
    async def create_question(self, question: QuestionRequest1) -> int:
        """
        Create a new question in the database.
        
        Args:
            question: Question data to insert
            
        Returns:
            ID of the created question
            
        Raises:
            ValueError: If insertion fails
        """
        answers_json = json.dumps(question.answers, ensure_ascii=False)
        rights_json = json.dumps(question.rights, ensure_ascii=False)
        
        query = """
        INSERT INTO questions (title, type, language, answers, rights)
        VALUES (?, ?, ?, ?, ?)
        """
        
        async with self.get_connection() as db:
            cursor = await db.execute(
                query,
                (question.title, question.type, question.language, answers_json, rights_json)
            )
            await db.commit()
            return cursor.lastrowid
    
    async def update_question(self, question: QuestionRequest1) -> int:
        """
        Update an existing question in the database.
        
        Args:
            question: Question data to update (must include id)
            
        Returns:
            Number of affected rows
            
        Raises:
            ValueError: If question ID is not provided or update fails
        """
        if not question.id:
            raise ValueError("Question ID is required for update")
        
        # Sort rights for consistency
        sorted_rights = sorted(question.rights)
        
        answers_json = json.dumps(question.answers, ensure_ascii=False)
        rights_json = json.dumps(sorted_rights, ensure_ascii=False)
        
        query = """
        UPDATE questions SET
            title = ?,
            type = ?,
            language = ?,
            answers = ?,
            rights = ?
        WHERE id = ?
        """
        
        affected_rows = await self.execute(
            query,
            (question.title, question.type, question.language, 
             answers_json, rights_json, question.id)
        )
        
        return affected_rows
    
    async def batch_insert_questions(self, questions: List[Dict[str, Any]]) -> None:
        """
        Batch insert multiple questions.
        
        Args:
            questions: List of question dictionaries
            
        Raises:
            ValueError: If batch insertion fails
        """
        query = """
        INSERT INTO questions (type, title, language, answers, rights)
        VALUES (?, ?, ?, ?, ?)
        """
        
        params_list = []
        for q in questions:
            answers_json = json.dumps(q["answers"], ensure_ascii=False)
            rights_json = json.dumps(q["rights"], ensure_ascii=False)
            
            params_list.append((
                q["type"],
                q["title"],
                q["language"],
                answers_json,
                rights_json
            ))
        
        async with self.get_connection() as db:
            await db.executemany(query, params_list)
            await db.commit()
    
    async def batch_delete_questions(self, question_ids: List[int]) -> int:
        """
        Batch delete questions by IDs.
        
        Args:
            question_ids: List of question IDs to delete
            
        Returns:
            Number of deleted rows
        """
        if not question_ids:
            return 0
        
        placeholders = ",".join("?" * len(question_ids))
        query = f"DELETE FROM questions WHERE id IN ({placeholders})"
        
        return await self.execute(query, tuple(question_ids))
    
    async def get_questions_paginated(
        self,
        page: int = 1,
        page_size: int = 10,
        search: str = "",
        question_type: Optional[int] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        Get paginated questions with optional search and type filter.
        
        Args:
            page: Page number (1-based)
            page_size: Number of items per page
            search: Search term for title
            question_type: Filter by question type
            
        Returns:
            Tuple of (questions list, total count)
        """
        # Build WHERE conditions
        conditions = []
        params = []
        
        if question_type is not None:
            conditions.append("type = ?")
            params.append(question_type)
        
        if search:
            conditions.append("title LIKE ?")
            params.append(f"%{search}%")
        
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        # Get total count
        count_query = f"SELECT COUNT(*) as count FROM questions {where_clause}"
        count_result = await self.get(count_query, tuple(params))
        total = count_result["count"] if count_result else 0
        
        # Get paginated data
        offset = (page - 1) * page_size
        data_query = f"""
        SELECT id, title, type
        FROM questions {where_clause}
        ORDER BY id DESC
        LIMIT ? OFFSET ?
        """
        
        params.extend([page_size, offset])
        questions = await self.select(data_query, tuple(params))
        
        return questions, total
    
    async def get_question_by_id(self, question_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a single question by ID with parsed JSON fields.
        
        Args:
            question_id: Question ID
            
        Returns:
            Question dictionary with parsed answers and rights, or None if not found
        """
        query = """
        SELECT id, type, title, language, answers, rights
        FROM questions
        WHERE id = ?
        """
        
        result = await self.get(query, (question_id,))
        if not result:
            return None
        
        # Parse JSON fields
        try:
            result["answers"] = json.loads(result["answers"])
            result["rights"] = json.loads(result["rights"])
        except json.JSONDecodeError:
            # Handle malformed JSON gracefully
            result["answers"] = []
            result["rights"] = []
        
        return result


async def init_database(db_path: str) -> Database:
    """
    Initialize database and return Database instance.
    
    Args:
        db_path: Path to SQLite database file
        
    Returns:
        Initialized Database instance
    """
    db = Database(db_path)
    await db.init_db()
    return db
