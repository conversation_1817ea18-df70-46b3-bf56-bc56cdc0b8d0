"""
题目服务的数据库操作模块。
处理SQLite数据库连接和CRUD操作。
"""

import json
import sqlite3
from typing import List, Dict, Any, Optional, Tuple
from contextlib import asynccontextmanager
import aiosqlite

from app.config.config import QuestionRequest1


CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS questions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    type INTEGER NOT NULL,
    language TEXT NOT NULL,
    answers TEXT NOT NULL,
    rights TEXT NOT NULL
);
"""


class Database:
    """SQLite操作的数据库包装器。"""

    def __init__(self, db_path: str):
        """
        初始化数据库连接。

        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path

    async def init_db(self) -> None:
        """初始化数据库并创建表。"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(CREATE_TABLE_SQL)
            await db.commit()

    async def close(self) -> None:
        """关闭数据库连接（兼容性占位符）。"""
        pass
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接上下文管理器。"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            yield db

    async def select(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        执行SELECT查询并返回结果。

        Args:
            query: SQL查询字符串
            params: 查询参数

        Returns:
            表示行的字典列表
        """
        async with self.get_connection() as db:
            cursor = await db.execute(query, params)
            rows = await cursor.fetchall()
            return [dict(row) for row in rows]

    async def get(self, query: str, params: tuple = ()) -> Optional[Dict[str, Any]]:
        """
        执行SELECT查询并返回单个结果。

        Args:
            query: SQL查询字符串
            params: 查询参数

        Returns:
            表示单行的字典或None
        """
        async with self.get_connection() as db:
            cursor = await db.execute(query, params)
            row = await cursor.fetchone()
            return dict(row) if row else None

    async def execute(self, query: str, params: tuple = ()) -> int:
        """
        执行INSERT/UPDATE/DELETE查询。

        Args:
            query: SQL查询字符串
            params: 查询参数

        Returns:
            受影响的行数
        """
        async with self.get_connection() as db:
            cursor = await db.execute(query, params)
            await db.commit()
            return cursor.rowcount

    async def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """
        使用多个参数集执行查询。

        Args:
            query: SQL查询字符串
            params_list: 参数元组列表

        Returns:
            受影响的行数
        """
        async with self.get_connection() as db:
            cursor = await db.executemany(query, params_list)
            await db.commit()
            return cursor.rowcount
    
    async def create_question(self, question: QuestionRequest1) -> int:
        """
        在数据库中创建新题目。

        Args:
            question: 要插入的题目数据

        Returns:
            创建的题目ID

        Raises:
            ValueError: 如果插入失败
        """
        answers_json = json.dumps(question.answers, ensure_ascii=False)
        rights_json = json.dumps(question.rights, ensure_ascii=False)

        query = """
        INSERT INTO questions (title, type, language, answers, rights)
        VALUES (?, ?, ?, ?, ?)
        """

        async with self.get_connection() as db:
            cursor = await db.execute(
                query,
                (question.title, question.type, question.language, answers_json, rights_json)
            )
            await db.commit()
            return cursor.lastrowid

    async def update_question(self, question: QuestionRequest1) -> int:
        """
        更新数据库中的现有题目。

        Args:
            question: 要更新的题目数据（必须包含id）

        Returns:
            受影响的行数

        Raises:
            ValueError: 如果未提供题目ID或更新失败
        """
        if not question.id:
            raise ValueError("Question ID is required for update")

        # 排序答案以保持一致性
        sorted_rights = sorted(question.rights)

        answers_json = json.dumps(question.answers, ensure_ascii=False)
        rights_json = json.dumps(sorted_rights, ensure_ascii=False)

        query = """
        UPDATE questions SET
            title = ?,
            type = ?,
            language = ?,
            answers = ?,
            rights = ?
        WHERE id = ?
        """

        affected_rows = await self.execute(
            query,
            (question.title, question.type, question.language,
             answers_json, rights_json, question.id)
        )

        return affected_rows
    
    async def batch_insert_questions(self, questions: List[Dict[str, Any]]) -> None:
        """
        批量插入多个题目。

        Args:
            questions: 题目字典列表

        Raises:
            ValueError: 如果批量插入失败
        """
        query = """
        INSERT INTO questions (type, title, language, answers, rights)
        VALUES (?, ?, ?, ?, ?)
        """

        params_list = []
        for q in questions:
            answers_json = json.dumps(q["answers"], ensure_ascii=False)
            rights_json = json.dumps(q["rights"], ensure_ascii=False)

            params_list.append((
                q["type"],
                q["title"],
                q["language"],
                answers_json,
                rights_json
            ))

        async with self.get_connection() as db:
            await db.executemany(query, params_list)
            await db.commit()

    async def batch_delete_questions(self, question_ids: List[int]) -> int:
        """
        根据ID批量删除题目。

        Args:
            question_ids: 要删除的题目ID列表

        Returns:
            删除的行数
        """
        if not question_ids:
            return 0

        placeholders = ",".join("?" * len(question_ids))
        query = f"DELETE FROM questions WHERE id IN ({placeholders})"

        return await self.execute(query, tuple(question_ids))
    
    async def get_questions_paginated(
        self,
        page: int = 1,
        page_size: int = 10,
        search: str = "",
        question_type: Optional[int] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取分页题目，支持可选的搜索和类型过滤。

        Args:
            page: 页码（从1开始）
            page_size: 每页项目数
            search: 标题搜索词
            question_type: 按题目类型过滤

        Returns:
            (题目列表, 总数)的元组
        """
        # 构建WHERE条件
        conditions = []
        params = []

        if question_type is not None:
            conditions.append("type = ?")
            params.append(question_type)

        if search:
            conditions.append("title LIKE ?")
            params.append(f"%{search}%")

        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)

        # 获取总数
        count_query = f"SELECT COUNT(*) as count FROM questions {where_clause}"
        count_result = await self.get(count_query, tuple(params))
        total = count_result["count"] if count_result else 0

        # 获取分页数据
        offset = (page - 1) * page_size
        data_query = f"""
        SELECT id, title, type
        FROM questions {where_clause}
        ORDER BY id DESC
        LIMIT ? OFFSET ?
        """

        params.extend([page_size, offset])
        questions = await self.select(data_query, tuple(params))

        return questions, total
    
    async def get_question_by_id(self, question_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取单个题目，并解析JSON字段。

        Args:
            question_id: 题目ID

        Returns:
            包含解析后答案和选项的题目字典，如果未找到则返回None
        """
        query = """
        SELECT id, type, title, language, answers, rights
        FROM questions
        WHERE id = ?
        """

        result = await self.get(query, (question_id,))
        if not result:
            return None

        # 解析JSON字段
        try:
            result["answers"] = json.loads(result["answers"])
            result["rights"] = json.loads(result["rights"])
        except json.JSONDecodeError:
            # 优雅地处理格式错误的JSON
            result["answers"] = []
            result["rights"] = []

        return result


async def init_database(db_path: str) -> Database:
    """
    初始化数据库并返回Database实例。

    Args:
        db_path: SQLite数据库文件路径

    Returns:
        初始化的Database实例
    """
    db = Database(db_path)
    await db.init_db()
    return db
