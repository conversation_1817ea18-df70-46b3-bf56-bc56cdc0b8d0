"""
Actions controller for question management and statistics.
Handles CRUD operations and data retrieval.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from app.config.config import QuestionRequest1, validate_question_request1
from app.storage.database import Database
from app.api.response import success_response, error_response


class PageRequest(BaseModel):
    """Request model for pagination."""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页大小")
    search: str = Field("", description="搜索关键词")


class DeleteRequest(BaseModel):
    """Request model for batch deletion."""
    ids: List[int] = Field(..., min_items=1, description="要删除的ID列表")


class ManualQuestionRequest(BaseModel):
    """Request model for manual question creation."""
    id: Optional[int] = Field(None, description="题目ID（更新时需要）")
    type: int = Field(..., ge=1, le=3, description="题目类型")
    title: str = Field(..., min_length=1, description="题目标题")
    language: str = Field(..., description="编程语言")
    answers: List[str] = Field(..., min_items=4, max_items=4, description="选项列表")
    rights: List[str] = Field(..., min_items=1, description="正确答案")


class ActionsController:
    """Controller for question management actions."""
    
    def __init__(self, database: Database):
        """
        Initialize actions controller.
        
        Args:
            database: Database instance
        """
        self.database = database
        self.router = APIRouter()
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup API routes."""
        self.router.get("/test")(self.test_endpoint)
        self.router.get("/summary")(self.summary)
        self.router.get("/bytype1")(self.by_type1)
        self.router.get("/bytype2")(self.by_type2)
        self.router.get("/bytype3")(self.by_type3)
        self.router.get("/byid/{question_id}")(self.by_id)
        self.router.post("/CreateByHand")(self.generate_question)
        self.router.put("/update")(self.update_question)
        self.router.delete("/batch-delete")(self.batch_delete)

    async def test_endpoint(self):
        """Test endpoint to verify routing."""
        print("DEBUG: test_endpoint called!")
        return success_response({"message": "Test endpoint working", "database": str(self.database)})

    async def _handle_pagination(
        self,
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100),
        search: str = Query(""),
        question_type: Optional[int] = None
    ):
        """
        Handle paginated question retrieval.

        Args:
            page: Page number
            page_size: Items per page
            search: Search term
            question_type: Filter by question type

        Returns:
            Paginated response
        """
        try:
            # Add debug logging
            print(f"DEBUG: _handle_pagination called with page={page}, page_size={page_size}, search='{search}', question_type={question_type}")
            print(f"DEBUG: Database instance: {self.database}")

            questions, total = await self.database.get_questions_paginated(
                page=page,
                page_size=page_size,
                search=search,
                question_type=question_type
            )

            print(f"DEBUG: Database returned total={total}, questions_count={len(questions)}")
            print(f"DEBUG: First few questions: {questions[:2] if questions else 'None'}")

            response_data = {
                "total": total,
                "questions": questions
            }

            print(f"DEBUG: Response data: {response_data}")

            return success_response(response_data)

        except Exception as e:
            print(f"DEBUG: Exception in _handle_pagination: {e}")
            import traceback
            traceback.print_exc()
            raise error_response(f"获取数据失败: {str(e)}", 500)
    
    async def summary(
        self,
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100),
        search: str = Query("")
    ):
        """Get all questions with pagination."""
        return await self._handle_pagination(page, page_size, search)
    
    async def by_type1(
        self,
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100),
        search: str = Query("")
    ):
        """Get single-select questions with pagination."""
        return await self._handle_pagination(page, page_size, search, 1)
    
    async def by_type2(
        self,
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100),
        search: str = Query("")
    ):
        """Get multi-select questions with pagination."""
        return await self._handle_pagination(page, page_size, search, 2)
    
    async def by_type3(
        self,
        page: int = Query(1, ge=1),
        page_size: int = Query(10, ge=1, le=100),
        search: str = Query("")
    ):
        """Get coding questions with pagination."""
        return await self._handle_pagination(page, page_size, search, 3)
    
    async def by_id(self, question_id: int):
        """
        Get question by ID.
        
        Args:
            question_id: Question ID
            
        Returns:
            Question details
        """
        try:
            if question_id <= 0:
                raise error_response(f"无效的题目ID: {question_id}", 400)
            
            question = await self.database.get_question_by_id(question_id)
            if not question:
                raise error_response("题目不存在", 404)
            
            return success_response(question)
            
        except HTTPException:
            raise
        except Exception as e:
            raise error_response(f"获取题目失败: {str(e)}", 500)
    
    async def generate_question(self, request: ManualQuestionRequest):
        """
        Create question manually.
        
        Args:
            request: Manual question creation request
            
        Returns:
            Creation success response
        """
        try:
            # Convert to internal format
            question_req = QuestionRequest1(
                type=request.type,
                title=request.title,
                language=request.language,
                answers=request.answers,
                rights=request.rights
            )
            
            # Validate request
            validate_question_request1(question_req)
            
            # Create question
            question_id = await self.database.create_question(question_req)
            
            return success_response({
                "insert_id": question_id,
                "message": "插入成功",
                "data": {
                    "type": request.type,
                    "title": request.title,
                    "language": request.language,
                    "answers": request.answers,
                    "rights": request.rights
                }
            })
            
        except ValueError as e:
            raise error_response(f"参数错误: {str(e)}", 400)
        except Exception as e:
            raise error_response(f"数据库创建失败: {str(e)}", 500)
    
    async def update_question(self, request: ManualQuestionRequest):
        """
        Update existing question.
        
        Args:
            request: Question update request
            
        Returns:
            Update success response
        """
        try:
            if not request.id:
                raise ValueError("更新操作需要提供题目ID")
            
            # Convert to internal format
            question_req = QuestionRequest1(
                id=request.id,
                type=request.type,
                title=request.title,
                language=request.language,
                answers=request.answers,
                rights=request.rights
            )
            
            # Validate request
            validate_question_request1(question_req)
            
            # Validate question type for update (only single/multi select)
            if request.type not in [1, 2]:
                raise ValueError("无效的题目类型")
            
            # Validate answer options
            valid_options = {"A", "B", "C", "D"}
            for answer in request.rights:
                if answer not in valid_options:
                    raise ValueError("存在无效选项标识")
            
            # Update question
            affected_rows = await self.database.update_question(question_req)
            
            return success_response({
                "affected_rows": affected_rows,
                "updated_data": {
                    "type": request.type,
                    "title": request.title,
                    "answers": request.answers,
                    "rights": sorted(request.rights)  # Sort for consistency
                }
            })
            
        except ValueError as e:
            raise error_response(f"参数格式错误: {str(e)}", 400)
        except Exception as e:
            raise error_response(f"更新失败: {str(e)}", 500)
    
    async def batch_delete(self, request: DeleteRequest):
        """
        Batch delete questions.
        
        Args:
            request: Delete request with question IDs
            
        Returns:
            Deletion success response
        """
        try:
            deleted_count = await self.database.batch_delete_questions(request.ids)
            
            return success_response({
                "deleted_ids": request.ids,
                "message": "删除成功",
                "deleted_count": deleted_count
            })
            
        except Exception as e:
            raise error_response(f"删除操作失败: {str(e)}", 500)


def create_actions_controller(database: Database) -> APIRouter:
    """
    Factory function to create actions controller router.
    
    Args:
        database: Database instance
        
    Returns:
        Configured APIRouter
    """
    controller = ActionsController(database)
    return controller.router
