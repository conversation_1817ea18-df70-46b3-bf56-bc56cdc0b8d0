<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        /* 名片容器样式 */
        .business-card {
            width: 500px;
            margin: 50px auto;
            padding: 30px;
            background-color: #f0f8ff;
            border-radius: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            font-family: Arial, sans-serif;
        }

        /* 头像样式 */
        .avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 3px solid #4CAF50;
            margin-bottom: 10px;
            object-fit: cover;
        }
        h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
            margin-left: 10px;
            text-align: center;
        }

        /* 个人信息样式 */
        .specialization {
            color: #4CAF50;
            font-size: 25px;
            font-weight: bold;
            margin-bottom: 15px;
            margin-left: 10px;
        }

        .lianxi {
            color: #00F5FF;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        /* 联系方式容器 */
        .contact-info {
            background-color: #e8f5e9;
            padding: 10px;
            border-radius: 10px;
        }

        .basic-info {
            display: flex; 
            align-items: center; 
            gap: 30px; 
            margin-bottom: 10px;
        }

        .info-text {
            flex: 1; 
        }

        /* 图标样式 */
        .icon {
            width: 20px;
            vertical-align: middle;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="business-card">
        <h1>个人名片</h1>
        <div class="basic-info">
            <img src="https://tse3-mm.cn.bing.net/th/id/OIP-C.PiJmmfWThahP2Pb-MpVGaAHaEh?rs=1&pid=ImgDetMain" 
                 alt="个人头像" 
                 class="avatar">

            <div class="info-text">
                <div class="specialization">姓名： 付坤</div>
                <div class="specialization">专业方向：客户端开发</div>
            </div>
        </div>

        <div class="contact-info">
            <div class="lianxi">联系方式</div>
            <p>
                邮箱： <EMAIL>
            </p>
            <p>
                电话：  ***********
            </p>
        </div>
    </div>
</body>
</html>