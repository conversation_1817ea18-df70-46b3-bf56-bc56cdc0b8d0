{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.24.9", "axios": "^1.9.0", "http-proxy-middleware": "^3.0.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "vite --port 3000", "build": "vite build", "preview": "vite preview --port 3000", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "vite": "^6.3.4"}}