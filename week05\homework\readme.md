此目录存放本周课后作业，可以在此文件添加作业设计思路和流程图等


## 技术亮点
1. 弹性参数处理
   - 智能转换字符串/数字类型
   - 自动处理空值和默认值

2. 模型抽象层
   - 统一接口规范（AIClient）
   - 多态调用不同AI实现

3. 高性能存储
   - 读写锁控制并发
   - 内存缓存加速
   - 按日滚动持久化
   
## 核心功能
- **双模型驱动**：DeepSeek / 通义千问双引擎支持  
- **智能参数处理**：空值自动填充 + 范围校验 + 类型转换  
- **安全存储方案**：按日分片存储 + JSON 标准化 + 互斥锁并发控制  
- **全链路可观测**：请求耗时统计 + 错误链路追踪 + 操作留痕  

## 架构设计
分层架构图：
- 接入层：API路由和参数校验
- 业务层：模型路由分发请求至DeepSeek或通义客户端
- 持久层：响应解析和JSON存储

## 核心流程
-客户端->>+服务端: POST /questions/create
-服务端->>+校验器: 参数标准化
-校验器->>+模型路由: 分发请求
-模型路由->>+AI平台: API调用
-AI平台-->>-模型路由: 原始响应
-模型路由->>+存储器: 日志落盘
-存储器-->>-客户端: 结构响应

## 演进规划
- 性能优化：二级缓存、请求合并
- 监控增强：集成Prometheus
- 功能扩展：PDF导出、错题本
- 安全升级：JWT鉴权、请求签名

