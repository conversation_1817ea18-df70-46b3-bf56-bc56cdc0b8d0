"""
AI Service client interface and implementation.
Provides unified interface for different AI providers.
"""

from abc import ABC, abstractmethod
from typing import Optional

from app.config.config import <PERSON><PERSON>onfig, QuestionRequest, QuestionResponses, validate_question_request
from app.services.deepseek import DeepSeekClient


class AIService(ABC):
    """Abstract base class for AI services."""
    
    @abstractmethod
    async def generate_question(self, req: QuestionRequest) -> QuestionResponses:
        """Generate questions using AI service."""
        pass


class AIServiceImpl(AIService):
    """Implementation of AI service with multiple providers."""
    
    def __init__(self, config: AIConfig):
        """
        Initialize AI service with configuration.

        Args:
            config: AI configuration with API keys and settings
        """
        self.deepseek: Optional[DeepSeekClient] = None

        # Initialize available clients
        if config.deepseek_key:
            self.deepseek = DeepSeekClient(config.deepseek_key, config.timeout)
    
    async def generate_question(self, req: QuestionRequest) -> QuestionResponses:
        """
        Generate questions using the specified AI model.

        Args:
            req: Question generation request

        Returns:
            QuestionResponses: Generated questions

        Raises:
            ValueError: If model is not supported or not configured
        """
        # Validate and set defaults
        req = validate_question_request(req)

        # Route to appropriate service
        if req.model == "deepseek" or req.model == "":
            if not self.deepseek:
                raise ValueError("DeepSeek API密钥未配置")
            return await self.deepseek.generate(req)

        else:
            raise ValueError("不支持的AI模型")


def create_ai_service(config: AIConfig) -> AIService:
    """
    Factory function to create AI service instance.
    
    Args:
        config: AI configuration
        
    Returns:
        AIService: Configured AI service instance
    """
    return AIServiceImpl(config)
