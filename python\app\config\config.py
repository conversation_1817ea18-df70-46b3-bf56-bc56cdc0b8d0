"""
Configuration management for the Question Service API.
Handles environment variables and application settings.
"""

import os
from typing import List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv


# Question type constants
SINGLE_SELECT = 1
MULTI_SELECT = 2
CODING = 3


@dataclass
class AIConfig:
    """AI service configuration."""
    deepseek_key: str
    timeout: int = 30  # seconds


@dataclass
class QuestionRequest:
    """Request structure for AI question generation."""
    keyword: str
    model: Optional[str] = None  # "deepseek", defaults to "deepseek"
    language: Optional[str] = None  # programming language, defaults to "go"
    count: Optional[int] = None  # number of questions, defaults to 3
    type: Optional[int] = None  # question type, defaults to 1


@dataclass
class QuestionResponse:
    """Response structure for a single question."""
    title: str
    answers: List[str]
    rights: List[str]


@dataclass
class QuestionResponses:
    """Response structure for multiple questions."""
    questions: List[QuestionResponse]


@dataclass
class QuestionRequest1:
    """Request structure for manual question creation/update."""
    id: Optional[int] = None
    type: int = SINGLE_SELECT
    title: str = ""
    language: str = ""
    answers: List[str] = None
    rights: List[str] = None

    def __post_init__(self):
        if self.answers is None:
            self.answers = []
        if self.rights is None:
            self.rights = []


def load_config() -> AIConfig:
    """
    Load configuration from environment variables.

    Returns:
        AIConfig: Configuration object with API keys and settings

    Raises:
        ValueError: If DeepSeek API key is not configured
    """
    # Load .env file if it exists
    load_dotenv()

    deepseek_key = os.getenv("DEEPSEEK_API_KEY", "")
    timeout = int(os.getenv("API_TIMEOUT", "30"))

    # DeepSeek API key must be configured
    if not deepseek_key:
        raise ValueError("必须配置DeepSeek API密钥（DEEPSEEK_API_KEY）")

    return AIConfig(
        deepseek_key=deepseek_key,
        timeout=timeout
    )


def validate_question_request(req: QuestionRequest) -> QuestionRequest:
    """
    Validate and set defaults for question request.
    
    Args:
        req: Question request to validate
        
    Returns:
        QuestionRequest: Validated request with defaults applied
        
    Raises:
        ValueError: If validation fails
    """
    if not req.keyword:
        raise ValueError("关键字不能为空")
    
    # Set defaults
    if not req.language:
        req.language = "go"
    if not req.type:
        req.type = SINGLE_SELECT
    if not req.count:
        req.count = 3
    if not req.model:
        req.model = "deepseek"

    # Validate values
    if req.model not in ["deepseek"]:
        raise ValueError("不支持的AI模型")
    
    if req.language not in ["go", "java", "python", "javascript", "c++", "css", "html"]:
        raise ValueError("不支持的编程语言")
    
    if req.count < 3 or req.count > 10:
        raise ValueError("题目数量必须在3-10之间")
    
    if req.type not in [SINGLE_SELECT, MULTI_SELECT, CODING]:
        raise ValueError("无效的题目类型")
    
    return req


def validate_question_request1(req: QuestionRequest1) -> None:
    """
    Validate manual question creation request.
    
    Args:
        req: Question request to validate
        
    Raises:
        ValueError: If validation fails
    """
    if not req.title:
        raise ValueError("题目标题不能为空")
    
    if req.type not in [SINGLE_SELECT, MULTI_SELECT, CODING]:
        raise ValueError("无效的题目类型")
    
    if len(req.answers) != 4:
        raise ValueError("必须提供4个选项")
    
    # Validate answer options for single/multi select
    if req.type in [SINGLE_SELECT, MULTI_SELECT]:
        valid_options = {"A", "B", "C", "D"}
        for answer in req.rights:
            if answer not in valid_options:
                raise ValueError("存在无效选项标识")
        
        # Check for duplicates
        if len(set(req.rights)) != len(req.rights):
            raise ValueError("答案选项不能重复")
        
        # Single select must have exactly one answer
        if req.type == SINGLE_SELECT and len(req.rights) != 1:
            raise ValueError("单选题必须有且仅有一个正确答案")
        
        # Multi select must have 2-4 answers
        if req.type == MULTI_SELECT and (len(req.rights) < 2 or len(req.rights) > 4):
            raise ValueError("多选题正确答案数量需在2-4个之间")
